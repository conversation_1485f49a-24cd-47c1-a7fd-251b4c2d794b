const RenderContentTransformer = require('../../../src/frameworks/vue/RenderContentTransformer');
const path = require('path');

describe('RenderContentTransformer', () => {
  let transformer;

  beforeEach(() => {
    transformer = new RenderContentTransformer({
      verbose: false,
      aiApiKey: process.env.GLM_API_KEY || process.env.OPENAI_API_KEY,
      aiProvider: 'auto'
    });
  });

  describe('hasRenderContent', () => {
    it('should detect renderContent method with Vue 2 signature', () => {
      const code = `
        renderContent(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;
      expect(transformer.hasRenderContent(code)).toBe(true);
    });

    it('should not detect Vue 3 renderContent signature', () => {
      const code = `
        renderContent({ node, data }) {
          return h('div', null, data.title)
        }
      `;
      expect(transformer.hasRenderContent(code)).toBe(false);
    });

    it('should not detect unrelated methods', () => {
      const code = `
        someOtherMethod(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;
      expect(transformer.hasRenderContent(code)).toBe(false);
    });
  });

  describe('transformWithAST', () => {
    it('should transform simple renderContent method', async () => {
      const input = `
        renderContent(h, { node, data }) {
          return (
            <div class="title">
              {data.title}
            </div>
          )
        }
      `;

      const result = await transformer.transformWithAST(input, 'test.vue');
      
      expect(result.success).toBe(true);
      expect(result.code).toContain('renderContent({ node, data })');
      expect(result.code).toContain("import { h } from 'vue'");
      expect(result.code).toContain("h('div'");
    });

    it('should handle complex renderContent with conditions', async () => {
      const input = `
        renderContent(h, { node, data }) {
          if (data.id === '-1') {
            return (
              <div class="custom-node">
                {data.title}
                <span style="opacity:0">{data.id}</span>
              </div>
            )
          } else {
            return (
              <div class="normal-node">
                {data.title}
              </div>
            )
          }
        }
      `;

      const result = await transformer.transformWithAST(input, 'test.vue');
      
      expect(result.success).toBe(true);
      expect(result.code).toContain('renderContent({ node, data })');
      expect(result.code).toContain("import { h } from 'vue'");
    });

    it('should return original code if no renderContent found', async () => {
      const input = `
        someOtherMethod() {
          return 'hello'
        }
      `;

      const result = await transformer.transformWithAST(input, 'test.vue');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('No renderContent methods found');
    });
  });

  describe('ensureHImport', () => {
    it('should add h import if not present', () => {
      const code = `
        <script>
        export default {
          methods: {
            test() {}
          }
        }
        </script>
      `;

      const result = transformer.ensureHImport(code);
      expect(result).toContain("import { h } from 'vue'");
    });

    it('should not duplicate h import if already present', () => {
      const code = `
        <script>
        import { h } from 'vue'
        export default {
          methods: {
            test() {}
          }
        }
        </script>
      `;

      const result = transformer.ensureHImport(code);
      const importMatches = result.match(/import.*h.*from.*vue/g);
      expect(importMatches).toHaveLength(1);
    });

    it('should add h to existing vue imports', () => {
      const code = `
        <script>
        import { ref, reactive } from 'vue'
        export default {
          methods: {
            test() {}
          }
        }
        </script>
      `;

      const result = transformer.ensureHImport(code);
      expect(result).toContain("import { h, ref, reactive } from 'vue'");
    });
  });

  describe('parseInlineStyle', () => {
    it('should parse CSS properties to camelCase object', () => {
      const styleStr = 'opacity:0;margin-top:10px;background-color:red';
      const result = transformer.parseInlineStyle(styleStr);
      
      expect(result).toEqual({
        opacity: 0,
        marginTop: '10px',
        backgroundColor: 'red'
      });
    });

    it('should handle numeric values', () => {
      const styleStr = 'width:100;height:200px';
      const result = transformer.parseInlineStyle(styleStr);
      
      expect(result).toEqual({
        width: 100,
        height: '200px'
      });
    });

    it('should handle empty style string', () => {
      const result = transformer.parseInlineStyle('');
      expect(result).toEqual({});
    });
  });

  describe('transform integration', () => {
    it('should transform complete Vue file with renderContent', async () => {
      const input = `
        <template>
          <el-tree :render-content="renderContent" />
        </template>
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div class="connect_id_title">
                  {data.title}{' '}
                  <span class="connect_id_number" style="opacity:0">
                    {data.id}
                  </span>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'test.vue');
      
      expect(result).toContain('renderContent({ node, data })');
      expect(result).toContain("import { h } from 'vue'");
      expect(result).toContain("h('div'");
      expect(result).not.toContain('renderContent(h,');
    });

    it('should return original code if no renderContent present', async () => {
      const input = `
        <template>
          <div>Hello World</div>
        </template>
        <script>
        export default {
          methods: {
            test() {
              return 'hello'
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'test.vue');
      expect(result).toBe(input);
    });
  });

  describe('statistics', () => {
    it('should track transformation statistics', async () => {
      const input = `
        renderContent(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;

      await transformer.transform(input, 'test1.vue');
      await transformer.transform(input, 'test2.vue');
      await transformer.transform('no renderContent here', 'test3.vue');

      const stats = transformer.getStats();
      expect(stats.total).toBe(3);
      expect(stats.astSuccess).toBe(2);
      expect(stats.failed).toBe(0);
      expect(stats.skipped).toBe(0);
      expect(stats.successRate).toBe('66.7%');
    });
  });
});
