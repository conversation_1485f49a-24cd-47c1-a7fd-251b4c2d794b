<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
    <p>{{ someData }}</p>
    <button @click="handleClick">Click me</button>
  </div>
</template>

<script>
// 移除了对不存在的组件的引用
export default {
  name: 'HelloWorld',
  props: {
    msg: String
  },
  data() {
    return {
      someData: 'test'
    }
  },
  methods: {
    handleClick() {
      // 在这里添加一个简单的处理函数，代替不存在的方法
      alert('Button clicked!');
    }
  }
}
</script>

<style scoped>
h1 {
  color: #42b983;
}
</style>