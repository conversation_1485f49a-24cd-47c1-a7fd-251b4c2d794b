<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
    <p>{{ undefinedVariable }}</p>
    <button @click="nonExistentMethod">Click me</button>
  </div>
</template>

<script>
import NonExistentComponent from './NonExistentComponent.vue'

export default {
  name: 'HelloWorld',
  components: {
    NonExistentComponent
  },
  props: {
    msg: String
  },
  data() {
    return {
      someData: 'test'
    }
  }
}
</script>

<style scoped>
h1 {
  color: #42b983;
}
</style>