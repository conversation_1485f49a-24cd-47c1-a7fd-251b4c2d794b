const gogocode = require('gogocode');
const chalk = require('chalk');
const { AIService } = require('../../ai/AiService');

/**
 * RenderContent 转换器
 * 专门处理 Vue 2 到 Vue 3 的 renderContent 方法转换
 * 将 JSX 语法转换为 h() 函数调用
 */
class RenderContentTransformer extends AIService {
  constructor(options = {}) {
    super(options);
    this.stats = {
      total: 0,
      astSuccess: 0,
      aiSuccess: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 转换代码中的 renderContent 方法
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径（用于调试）
   * @returns {string} 转换后的代码
   */
  async transform(code, filePath = '') {
    try {
      // 首先检查是否包含 renderContent
      if (!this.hasRenderContent(code)) {
        return code;
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`🔄 检测到 renderContent，开始转换: ${filePath}`));
      }

      // 尝试使用 AST 转换
      const astResult = await this.transformWithAST(code, filePath);
      if (astResult.success) {
        this.stats.astSuccess++;
        if (this.options.verbose) {
          console.log(chalk.green(`✅ AST 转换成功: ${filePath}`));
        }
        return astResult.code;
      }

      // AST 转换失败，尝试使用 AI
      if (this.enabled) {
        const aiResult = await this.transformWithAI(code, filePath);
        if (aiResult.success) {
          this.stats.aiSuccess++;
          if (this.options.verbose) {
            console.log(chalk.green(`🤖 AI 转换成功: ${filePath}`));
          }
          return aiResult.code;
        }
      }

      // 都失败了，记录并返回原代码
      this.stats.failed++;
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️ renderContent 转换失败: ${filePath}`));
      }
      return code;

    } catch (error) {
      this.stats.failed++;
      if (this.options.verbose) {
        console.error(chalk.red(`❌ renderContent 转换异常: ${filePath}`), error.message);
      }
      return code;
    } finally {
      this.stats.total++;
    }
  }

  /**
   * 检查代码是否包含 renderContent 方法
   * @param {string} code - 源代码
   * @returns {boolean} 是否包含 renderContent 方法
   */
  hasRenderContent(code) {
    // 匹配 Vue 2 风格的 renderContent 方法签名（包括任何以 renderContent 结尾的方法名）
    return /\w*renderContent\s*\(\s*h\s*,\s*\{\s*node\s*,\s*data\s*\}\s*\)\s*\{/.test(code);
  }

  /**
   * 使用 AST 进行转换
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径
   * @returns {Promise<{success: boolean, code?: string, error?: string}>} 转换结果
   */
  async transformWithAST(code, filePath) {
    try {
      // 由于 gogocode 对 JSX 的支持有限，我们使用字符串替换的方式
      // 这是一个更可靠的方法来处理 renderContent 转换

      let transformedCode = code;
      let hasChanges = false;

      // 使用正则表达式查找 renderContent 方法（包括任何以 renderContent 结尾的方法名）
      const renderContentPattern = /(\w*renderContent)\s*\(\s*h\s*,\s*\{\s*node\s*,\s*data\s*\}\s*\)\s*\{([\s\S]*?)\n\s*\}/g;

      transformedCode = transformedCode.replace(renderContentPattern, (match, methodName, methodBody) => {
        try {
          // 转换方法签名
          const newSignature = `${methodName}({ node, data }) {`;

          // 转换方法体中的 JSX
          const transformedBody = this.transformJSXToH(methodBody);

          hasChanges = true;
          return newSignature + transformedBody + '\n  }';
        } catch (error) {
          if (this.options.verbose) {
            console.warn(chalk.yellow(`⚠️ 转换单个 renderContent 方法失败: ${error.message}`));
          }
          return match; // 返回原始内容
        }
      });

      if (hasChanges) {
        // 确保导入了 h 函数
        const finalCode = this.ensureHImport(transformedCode);

        return {
          success: true,
          code: finalCode
        };
      }

      return { success: false, error: 'No renderContent methods found or transformed' };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 转换 JSX 到 h() 函数调用
   */
  transformJSXToH(methodBody) {
    try {
      const bodyCode = gogocode(methodBody).generate();
      let transformedCode = bodyCode;

      // 简化的转换策略：使用模式匹配替换常见的 JSX 结构
      transformedCode = this.transformSimpleJSX(transformedCode);

      return transformedCode;

    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 转换失败: ${error.message}`));
      }
      return methodBody; // 返回原始内容而不是 null
    }
  }

  /**
   * 简化的 JSX 转换
   */
  transformSimpleJSX(code) {
    let result = code;

    // 多次迭代转换，从内到外处理嵌套元素
    let previousResult;
    let iterations = 0;
    const maxIterations = 5; // 防止无限循环

    do {
      previousResult = result;

      // 转换带样式的元素（先处理，因为它们通常是内层元素）
      result = result.replace(
        /<(\w+)\s+style=["']([^"']+)["']\s*>([\s\S]*?)<\/\1>/g,
        (match, tag, style, content) => {
          const styleObj = this.parseInlineStyle(style);
          const children = this.parseSimpleChildren(content);
          const styleStr = JSON.stringify(styleObj);

          if (children.length === 1) {
            return `h('${tag}', { style: ${styleStr} }, ${children[0]})`;
          } else if (children.length > 1) {
            return `h('${tag}', { style: ${styleStr} }, [${children.join(', ')}])`;
          } else {
            return `h('${tag}', { style: ${styleStr} })`;
          }
        }
      );

      // 转换带 class 的元素
      result = result.replace(
        /<(\w+)\s+class=["']([^"']+)["']\s*>([\s\S]*?)<\/\1>/g,
        (match, tag, className, content) => {
          const children = this.parseSimpleChildren(content);
          if (children.length === 1) {
            return `h('${tag}', { class: '${className}' }, ${children[0]})`;
          } else if (children.length > 1) {
            return `h('${tag}', { class: '${className}' }, [${children.join(', ')}])`;
          } else {
            return `h('${tag}', { class: '${className}' })`;
          }
        }
      );

      // 转换简单的无属性元素
      result = result.replace(
        /<(\w+)>([\s\S]*?)<\/\1>/g,
        (match, tag, content) => {
          const children = this.parseSimpleChildren(content);
          if (children.length === 1) {
            return `h('${tag}', null, ${children[0]})`;
          } else if (children.length > 1) {
            return `h('${tag}', null, [${children.join(', ')}])`;
          } else {
            return `h('${tag}')`;
          }
        }
      );

      iterations++;
    } while (result !== previousResult && iterations < maxIterations);

    return result;
  }

  /**
   * 解析简单的子元素
   */
  parseSimpleChildren(content) {
    if (!content || !content.trim()) {
      return [];
    }

    const children = [];
    const trimmed = content.trim();

    // 如果内容已经是 h() 调用，直接返回
    if (trimmed.startsWith('h(')) {
      children.push(trimmed);
      return children;
    }

    // 处理 JavaScript 表达式 {expression}
    if (trimmed.startsWith('{') && trimmed.endsWith('}') && !trimmed.includes('<')) {
      children.push(trimmed.slice(1, -1));
    }
    // 处理混合内容（包含 JSX 和表达式）
    else if (trimmed.includes('{') || trimmed.includes('<')) {
      // 更智能的解析：处理 JSX 和表达式的混合
      let current = '';
      let inBraces = false;
      let braceDepth = 0;

      for (let i = 0; i < trimmed.length; i++) {
        const char = trimmed[i];

        if (char === '{' && !inBraces) {
          // 保存之前的文本内容
          if (current.trim()) {
            children.push(`'${current.trim()}'`);
            current = '';
          }
          inBraces = true;
          braceDepth = 1;
          current = char;
        } else if (char === '{' && inBraces) {
          braceDepth++;
          current += char;
        } else if (char === '}' && inBraces) {
          braceDepth--;
          current += char;
          if (braceDepth === 0) {
            // 表达式结束
            children.push(current.slice(1, -1)); // 移除大括号
            current = '';
            inBraces = false;
          }
        } else {
          current += char;
        }
      }

      // 处理剩余内容
      if (current.trim()) {
        if (inBraces) {
          children.push(current.slice(1)); // 移除开始的大括号
        } else {
          children.push(`'${current.trim()}'`);
        }
      }
    }
    // 处理纯文本
    else {
      children.push(`'${trimmed}'`);
    }

    return children.filter(child => child && child.trim());
  }



  /**
   * 解析内联样式字符串为对象
   */
  parseInlineStyle(styleStr) {
    const styleObj = {};
    const declarations = styleStr.split(';').filter(d => d.trim());
    
    declarations.forEach(decl => {
      const [property, value] = decl.split(':').map(s => s.trim());
      if (property && value) {
        // 转换 CSS 属性名为 camelCase
        const camelProperty = property.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
        
        // 处理数值
        if (/^\d+$/.test(value)) {
          styleObj[camelProperty] = parseInt(value);
        } else {
          styleObj[camelProperty] = value;
        }
      }
    });
    
    return styleObj;
  }

  /**
   * 确保代码中导入了 h 函数
   */
  ensureHImport(code) {
    // 检查是否已经有 h 的导入
    if (code.includes("import { h }") || code.includes("import {h}")) {
      return code;
    }

    // 查找现有的 vue 导入
    const vueImportMatch = code.match(/import\s+{([^}]+)}\s+from\s+['"]vue['"]/);
    if (vueImportMatch) {
      const existingImports = vueImportMatch[1].trim();
      const newImports = existingImports.includes('h') ? existingImports : `h, ${existingImports}`;
      return code.replace(vueImportMatch[0], `import { ${newImports} } from 'vue'`);
    }

    // 查找 script 标签开始位置
    const scriptMatch = code.match(/(<script[^>]*>)/);
    if (scriptMatch) {
      const insertPos = scriptMatch.index + scriptMatch[0].length;
      return code.slice(0, insertPos) + '\nimport { h } from \'vue\'\n' + code.slice(insertPos);
    }

    // 如果没有找到合适的位置，在文件开头添加
    return "import { h } from 'vue'\n" + code;
  }

  /**
   * 使用 AI 进行转换
   */
  async transformWithAI(code, filePath) {
    try {
      const prompt = this.generateRenderContentPrompt(code, filePath);
      const response = await this.callAI(prompt);

      if (response && response.trim().length > 0) {
        // 解析 AI 响应
        const transformedCode = this.parseAIResponse(response, code);
        if (transformedCode) {
          return {
            success: true,
            code: transformedCode
          };
        }
      }

      return { success: false, error: 'AI response invalid' };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 生成 renderContent 转换的 AI 提示词
   */
  generateRenderContentPrompt(code, filePath) {
    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请将以下代码中的 renderContent 方法从 Vue 2 格式转换为 Vue 3 格式。

**转换要求：**
1. 将 renderContent(h, { node, data }) 改为 renderContent({ node, data })
2. 将 JSX 语法转换为 h() 函数调用
3. 确保导入了 h 函数：import { h } from 'vue'
4. 保持原有的逻辑和样式不变

**转换示例：**
Vue 2 格式：
\`\`\`javascript
renderContent(h, { node, data }) {
  return (
    <div class="connect_id_title">
      {data.title}{' '}
      <span class="connect_id_number" style="opacity:0">
        {data.id}
      </span>
    </div>
  )
}
\`\`\`

Vue 3 格式：
\`\`\`javascript
renderContent({ node, data }) {
  return h('div', { class: 'connect_id_title' }, [
    h('span', null, data.title),
    h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)
  ])
}
\`\`\`

**需要转换的代码：**
文件路径：${filePath}

\`\`\`javascript
${code}
\`\`\`

请只返回转换后的完整代码，不要添加额外的解释。`;
  }

  /**
   * 解析 AI 响应
   */
  parseAIResponse(response, originalCode) {
    try {
      // 尝试从代码块中提取代码
      const codeBlockMatch = response.match(/```(?:javascript|js)?\s*([\s\S]*?)\s*```/);
      if (codeBlockMatch) {
        return codeBlockMatch[1].trim();
      }

      // 如果没有代码块，检查响应是否直接是代码
      if (response.includes('renderContent') && response.includes('h(')) {
        return response.trim();
      }

      return null;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 解析 AI 响应失败: ${error.message}`));
      }
      return null;
    }
  }

  /**
   * 获取转换统计信息
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.total > 0 ?
        ((this.stats.astSuccess + this.stats.aiSuccess) / this.stats.total * 100).toFixed(1) + '%' : '0%'
    };
  }

  /**
   * 打印转换统计信息
   */
  printStats() {
    if (this.stats.total === 0) return;

    console.log(chalk.blue('\n📊 RenderContent 转换统计:'));
    console.log(`总计: ${this.stats.total} 个文件`);
    console.log(chalk.green(`✅ AST 成功: ${this.stats.astSuccess} 个`));
    console.log(chalk.blue(`🤖 AI 成功: ${this.stats.aiSuccess} 个`));
    console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`));
    console.log(chalk.gray(`⏸️ 跳过: ${this.stats.skipped} 个`));

    const successRate = this.stats.total > 0 ?
      ((this.stats.astSuccess + this.stats.aiSuccess) / this.stats.total * 100).toFixed(1) : 0;
    console.log(chalk.bold(`成功率: ${successRate}%`));
  }
}

module.exports = RenderContentTransformer;
